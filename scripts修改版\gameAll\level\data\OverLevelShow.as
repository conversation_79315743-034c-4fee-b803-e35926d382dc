package gameAll.level.data
{
   import dataAll._app.worldMap.define.MapMode;
   import dataAll._app.worldMap.define.WorldMapDefine;
   import dataAll.level.define.LevelDefine;
   import gameAll.level.LevelGroup;
   import gameAll.level.PlayMode;
   
   public class OverLevelShow
   {
      
      public static var ORDER:String = "order";
      
      public static var ARENA_FAIL:String = "arenaFail";
      
      public static var UI_CLICK:String = "uiClick";
      
      public static var TASK_TIME_OVER:String = "taskTimeOver";
      
      public var from:String = "";
      
      public var winB:Boolean = false;
      
      public var model:String = "";
      
      public var worldMapModel:String = "";
      
      public var arenaNormalB:Boolean = false;
      
      private var unionBattleB:Boolean = false;
      
      public var levelDat:LevelData;
      
      public var levelDef:LevelDefine;
      
      public var enemyLv:int = 0;
      
      public var mapDefine:WorldMapDefine;
      
      public var gameOverUIB:Boolean = false;
      
      public var overBackMapB:Boolean = false;
      
      public var lotteryB:Boolean = false;
      
      public var saveB:Boolean = false;
      
      private var lottery200:Boolean = false;
      
      public function OverLevelShow()
      {
         super();
      }
      
      public function inData(from0:String, winB0:Boolean, model0:String, worldMapModel0:String, enemyLv0:int, mapDefine0:WorldMapDefine, arenaNormalB0:Boolean, lg0:LevelGroup) : void
      {
         this.from = from0;
         this.winB = winB0;
         this.model = model0;
         this.worldMapModel = worldMapModel0;
         this.enemyLv = enemyLv0;
         this.mapDefine = mapDefine0;
         this.arenaNormalB = arenaNormalB0;
         this.levelDat = lg0.nowLevel.dat;
         this.levelDef = lg0.nowLevel.define;
         this.unionBattleB = lg0.isUnionBattleB();
         this.gameOverUIB = this.pan_gameOverUIB();
         this.lotteryB = this.pan_lotteryB();
         this.saveB = this.pan_saveB(lg0);
      }
      
      public function overBackB() : Boolean
      {
         if(Boolean(this.levelDef))
         {
            return this.levelDef.info.overBackB;
         }
         return false;
      }
      
      private function pan_gameOverUIB() : Boolean
      {
         if(this.model == PlayMode.NORMAL)
         {
            if(this.worldMapModel == MapMode.ENDLESS)
            {
               return !this.winB;
            }
            return this.winB;
         }
         return true;
      }
      
      private function pan_lotteryB() : Boolean
      {
         return false;
      }
      
      private function pan_saveB(lg0:LevelGroup) : Boolean
      {
         return true;
      }
   }
}
